import { useEffect, useRef, useState } from "react";
import chroma from "chroma-js";
import { motion } from "framer-motion";

import { prominent } from 'color.js'
import { Vibrant } from "node-vibrant/browser"
import { But<PERSON> } from "@heroui/button";
import { Ava<PERSON>, Slider, Tooltip } from "@heroui/react";

// YouTube API Response Types
interface YouTubeBasicInfo {
    id: string;
    channel_id: string;
    title: string;
    thumbnail: Array<{ url: string }>;
}

interface YouTubeAuthor {
    name: string;
    thumbnails: Array<{ url: string }>;
    badges: Array<{ icon_type: string }>;
}

interface YouTubeOwner {
    author: YouTubeAuthor;
    subscriber_count: { text: string };
}

interface YouTubeSecondaryInfo {
    owner?: YouTubeOwner;
}

interface YouTubeStreamingFormat {
    itag: number;
    mime_type: string;
    url?: string;
}

interface YouTubeStreamingData {
    formats?: YouTubeStreamingFormat[];
    adaptive_formats?: YouTubeStreamingFormat[];
    expires: string;
}

interface YouTubeVideoData {
    basic_info: YouTubeBasicInfo;
    secondary_info?: YouTubeSecondaryInfo;
    streaming_data?: YouTubeStreamingData;
}

interface YouTubeAPIResponse {
    data: {
        data: YouTubeVideoData;
        streaming: string;
    };
    status: number;
    statusText: string;
}


interface YouTubeVideoProps {
    videoData: YouTubeAPIResponse;
}

export default function YouTubeVideo({ videoData }: YouTubeVideoProps) {
    const [videoInfo, setVideoInfo] = useState<YouTubeVideoData>(videoData.data.data);
    const [imageURL, setImageURL] = useState<string>(
        videoData.data.data.basic_info.thumbnail?.[0]?.url
            ?.replace("https://i.ytimg.com/vi/", "./api/youtube/thumbnail/")
            ?.replace("https://i.ytimg.com/vi_webp/", "./api/youtube/thumbnailwebp/") || ""
    );
    const [style, setStyle] = useState<string>("");
    const [midColor, setMidColor] = useState<string>("#000000");

    const videoImageRef = useRef<HTMLImageElement>(null);

    useEffect(() => {
        console.log(videoInfo.secondary_info?.owner?.author.badges);

        prominent(imageURL, { amount: 1, format: "hex" }).then((Colors) => {
            const gradient = generateNowPlayingGradient(Colors as string)
            console.log(gradient)
            setStyle(gradient);
        }).catch(console.error);
    }, [imageURL]);

    function ensureMinLightness(color: string, minLightness = 0.8): string {
        const c = chroma(color);
        // Get current HSL [hue, saturation, lightness]
        const [h, s, l] = c.hsl();

        // If it's already light enough, return as-is (normalized)
        if (l >= minLightness) {
            return c.hex();
        }

        // Otherwise, create a new color with the same hue & saturation but minLightness
        const adjusted = chroma.hsl(h, s, minLightness);
        console.log(h, s, l, adjusted, adjusted.hex())
        return adjusted.hex();
    }

    function ensureMinLightnessBackground(color: string, minLightness = 0.2): string {
        const c = chroma(color);
        // Get current HSL [hue, saturation, lightness]
        const [h, s, l] = c.hsl();

        // Check for colors too close to black or white
        const isWhite = l > 0.95 && s < 0.1;
        const isBlack = l < 0.1 && s < 0.1;

        if (isWhite || isBlack) {
            return '#555555'; // fallback gray
        }

        // If it's already light enough, return as-is (normalized)
        if (l >= minLightness) {
            return c.hex();
        }

        // Otherwise, create a new color with the same hue & saturation but minLightness
        const adjusted = chroma.hsl(h, s, minLightness);
        console.log(h, s, l, adjusted, adjusted.hex())
        return adjusted.hex();
    }

    function generateNowPlayingGradient(color: string) {
        const mid = chroma(color);
        setMidColor(ensureMinLightness(mid.hex()))

        return `${ensureMinLightnessBackground(mid.hex())}`;
    }

    return (
        <div className="w-full h-full absolute bg-black/50 backdrop-blur-xs flex flex-row items-center justify-center">
            <motion.div initial={{ opacity: 0, scale: 0.8 }} animate={{ opacity: 1, scale: 1 }} transition={{ duration: 0.5 }} className="absolute flex flex-col p-4 rounded-medium transition-background backdrop-blur-[2px] bg-clip-padding backdrop-filter backdrop-blur backdrop-saturate-100 backdrop-contrast-100" style={{ background: style ? style : "#0f0f0f60" }}>
                <div className="flex w-full justify-between items-center">
                    <div className="flex flex-row gap-1 items-center pb-2">
                        <svg role="img" className="size-3" viewBox="0 0 24 24" style={{ fill: midColor }} xmlns="http://www.w3.org/2000/svg"><path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" /></svg>
                        <p style={{ color: midColor }} className="text-sm">YouTube Video</p>
                    </div>
                    <div>
                        <Button className="text-white hover:bg-white/40 cursor-pointer pointer-events-auto min-w-0 w-auto h-auto p-1 rounded-full" isIconOnly aria-label="Close" color="default" variant="light">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                            </svg>
                        </Button>
                    </div>
                </div>
                <div className="flex flex-col relative">
                    <MoviePlayer ImageURL={imageURL} Response={{ data: videoInfo }} streaming={videoData.data.streaming} />
                    <h1 className="text-md font-bold pt-2" style={{ color: midColor }}>{videoInfo.basic_info.title}</h1>
                    <div className="flex flex-row gap-3 py-1">
                        <div className="w-10 h-10">
                            <Avatar src={videoInfo.secondary_info?.owner?.author.thumbnails?.[0]?.url?.replace("https://yt3.ggpht.com/", "./api/youtube/userAvatar/") || ""} />
                        </div>
                        <div className="flex flex-col justify-center w-full">
                            <div className="flex flex-row gap-1">
                                <h1 className="text-sm font-bold" style={{ color: midColor }}>{videoInfo.secondary_info?.owner?.author.name.replace(" - Topic", "")}</h1>
                                <div>
                                    {videoInfo.secondary_info?.owner?.author.badges?.map((badge, index) => {
                                        // @ts-ignore
                                        switch (badge.icon_type) {
                                            case "AUDIO_BADGE":
                                                return (
                                                    <VerifiedArtistBadge key={index} Color={midColor} />
                                                )
                                            case "CHECK_CIRCLE_THICK":
                                                return (
                                                    <VerifiedChannelBadge key={index} Color={midColor} />
                                                )
                                            default:
                                                return (
                                                    <div key={index}></div>
                                                )
                                        }
                                    })}
                                </div>
                            </div>
                            <p className="text-xs font-bormal" style={{ color: midColor + "cc" }}>{videoInfo.secondary_info?.owner?.subscriber_count.text}</p>
                        </div>
                    </div>
                    {videoInfo.secondary_info.description.text && (
                        <div className="mt-2 max-w-[500px] p-2 rounded-medium text-wrap flex-wrap text-ellipsis" style={{ background: midColor + "0e" }}>
                            <h1 style={{ color: midColor }} className="text-sm font-bold">Description</h1>
                            <p className="text-xs" style={{ color: midColor + "cc" }}>{videoInfo.secondary_info?.description.text ? videoInfo.secondary_info?.description.text.split("\n") : ""}</p>
                        </div>
                    )}
                </div>
            </motion.div>
        </div>
    )
}

export function VerifiedArtistBadge({ Color }: { Color: string }) {
    return (
        <Tooltip content="Official Artist Channel" closeDelay={0} delay={0} showArrow={true}>
            <svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 16 16" width="16" focusable="false" aria-hidden="true" style={{ fill: Color, userSelect: "none", display: "inherit", width: "100%", height: "100%", cursor: "pointer" }}><path clip-rule="evenodd" d="M13.053 5.906a2.1 2.1 0 01.002 4.188 2.1 2.1 0 01-2.963 2.961 2.1 2.1 0 01-4.189.003 2.1 2.1 0 01-2.96-2.964 2.1 2.1 0 01-.002-4.188 2.1 2.1 0 012.962-2.961 2.1 2.1 0 014.189-.001 2.1 2.1 0 012.961 2.962ZM7.999 4v4.668a1.75 1.75 0 101 1.582V6h2V4h-3Z" fill-rule="evenodd"></path></svg>
        </Tooltip>
    )
}

export function VerifiedChannelBadge({ Color }: { Color: string }) {
    return (
        <Tooltip content="Verified" closeDelay={0} delay={0} showArrow={true}>
            <svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 24 24" width="16" focusable="false" aria-hidden="true" style={{ fill: Color, userSelect: "none", display: "inherit", width: "100%", height: "100%", cursor: "pointer" }}><path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zM9.8 17.3l-4.2-4.1L7 11.8l2.8 2.7L17 7.4l1.4 1.4-8.6 8.5z"></path></svg>
        </Tooltip>
    )
}

export function MoviePlayer({ ImageURL, Response, streaming }: { ImageURL: string, Response: { data: YouTubeVideoData }, streaming: string }) {
    const VideoImageRef = useRef<HTMLImageElement>(null);
    const VideoRef = useRef<HTMLVideoElement>(null);
    const VideoSrc = useRef<HTMLSourceElement>(null);
    const [PlayerStatus, SetPlayerStatus] = useState("WAITING");
    const [ControlsVisibility, SetControlsVisibility] = useState(true);
    const [PlaybackTime, SetPlaybackTime] = useState("0:00");
    const [Duration, SetDuration] = useState("0:00");

    useEffect(() => {
        console.log(Response.data)

        const Format = Response.data.streaming_data?.formats;
        const VideoFormat = Format?.[0]
        const videoElement = VideoRef.current;

        if (!videoElement || !VideoFormat) return;

        videoElement.src = "http://localhost:3000/v1/PlayVideoStream?url=" + btoa(streaming)

        // Playback status handlers
        videoElement.addEventListener("waiting", () => SetPlayerStatus("BUFFERING"));
    }, []);


    function StartPlaying() {
        const videoElement = document.querySelector("video");

        if (!videoElement) return;
        videoElement.src = "http://localhost:3000/v1/PlayVideoStream?url=" + btoa(streaming)

        videoElement.addEventListener("canplay", () => {
            document.querySelector("video")?.play().catch((e) => {
                if (e) return SetPlayerStatus("STREAM_BROKE")
            })
        })
    }

    function FixBrokenStream() {
        const videoElement = document.querySelector("video");

        if (!videoElement) return;
        videoElement.src = ""
        videoElement.src = "http://localhost:3000/v1/PlayVideoStream?url=" + btoa(streaming)

        videoElement.addEventListener("canplay", () => {
            document.querySelector("video")?.play().catch((e) => {
                if (e) return SetPlayerStatus("STREAM_BROKE")
            })
        })
    }

    function HandlePlayButtonClick() {
        document.querySelector("video")?.play()
    }

    function HandlePauseButtonClick() {
        document.querySelector("video")?.pause()
    }

    function EnterFullscreen() {
        if (document.querySelector("video")?.requestFullscreen) {
            document.querySelector("video")?.requestFullscreen();
            // @ts-ignore
        } else if (document.querySelector("video")?.webkitRequestFullscreen) { // Safari
            // @ts-ignore
            document.querySelector("video")?.webkitRequestFullscreen();
            // @ts-ignore
        } else if (document.querySelector("video")?.msRequestFullscreen) { // IE11
            // @ts-ignore
            document.querySelector("video")?.msRequestFullscreen();
        }
    }

    function HandlePlaybackTimeUpdate() {
        const formatTime = (time: number) => {
            const minutes = Math.floor(time / 60).toString().padStart(2, '0');
            const seconds = Math.floor(time % 60).toString().padStart(2, '0');
            return `${minutes}:${seconds}`;
        };

        SetPlaybackTime(formatTime(document.querySelector("video")?.currentTime || 0))
    }

    function SetPlaybackDuration() {
        const formatTime = (time: number) => {
            const minutes = Math.floor(time / 60).toString().padStart(2, '0');
            const seconds = Math.floor(time % 60).toString().padStart(2, '0');
            return `${minutes}:${seconds}`;
        };

        SetDuration(formatTime(document.querySelector("video")?.duration || 0))
    }

    return (
        <div>
            <div className="relative">
                <div onMouseEnter={() => { SetControlsVisibility(true) }} onMouseLeave={() => { setTimeout(() => { if (PlayerStatus === "WAITING" || PlayerStatus === "STREAM_BROKE") { return } SetControlsVisibility(false) }, 1000) }} className={`absolute top-0 left-0 w-full h-full flex flex-row items-center justify-center ${PlayerStatus === "PAUSED" ? "opacity-100" : "opacity-0"} rounded-medium transition-opacity duration-500 hover:opacity-100`}>
                    {PlayerStatus === "STREAM_BROKE" ? <div className="flex flex-col items-center justify-center gap-2 absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="white" className="size-5">
                            <path d="M1 13.75V7.182L9.818 16H3.25A2.25 2.25 0 0 1 1 13.75ZM13 6.25v6.568L4.182 4h6.568A2.25 2.25 0 0 1 13 6.25ZM19 4.75a.75.75 0 0 0-1.28-.53l-3 3a.75.75 0 0 0-.22.53v4.5c0 .199.079.39.22.53l3 3a.75.75 0 0 0 1.28-.53V4.75ZM2.28 4.22a.75.75 0 0 0-1.06 1.06l10.5 10.5a.75.75 0 1 0 1.06-1.06L2.28 4.22Z" />
                        </svg>
                        <h1 className="text-white text-sm font-bold">playback error</h1>
                        <p className="text-white text-xs text-center">yeah, sometimes youtube hungs up on us... but you can try it again, maybe it helps.</p>
                        <Button variant="flat" size="sm" radius="full" className="text-white" onPress={FixBrokenStream}>try again</Button>
                    </div> : PlayerStatus === "WAITING" ? (
                        <div onClick={StartPlaying} className="pointer-events-auto">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="hover:scale-110 size-12 cursor-pointer">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm14.024-.983a1.125 1.125 0 0 1 0 1.966l-5.603 3.113A1.125 1.125 0 0 1 9 15.113V8.887c0-.857.921-1.4 1.671-.983l5.603 3.113Z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    ) : PlayerStatus === "ENDED" ? (
                        <div onClick={HandlePlayButtonClick} className="pointer-events-auto">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="hover:scale-110 size-12 cursor-pointer">
                                <path fill-rule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm14.024-.983a1.125 1.125 0 0 1 0 1.966l-5.603 3.113A1.125 1.125 0 0 1 9 15.113V8.887c0-.857.921-1.4 1.671-.983l5.603 3.113Z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    ) : (
                        <div className={`absolute bottom-0 left-0 h-10 bg-black/50 w-full flex flex-row items-center justify-between ${PlayerStatus === "WAITING" && "hidden"} rounded-b-medium`}>
                            <div className="flex flex-row gap-1">
                                {PlayerStatus === "PLAYING" ? (
                                    <div onClick={HandlePauseButtonClick} className="w-10 h-10 flex items-center justify-center cursor-pointer">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="size-6">
                                            <path fillRule="evenodd" d="M6.75 5.25a.75.75 0 0 1 .75-.75H9a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H7.5a.75.75 0 0 1-.75-.75V5.25Zm7.5 0A.75.75 0 0 1 15 4.5h1.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H15a.75.75 0 0 1-.75-.75V5.25Z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                ) : (
                                    <div onClick={HandlePlayButtonClick} className="w-10 h-10 flex items-center justify-center cursor-pointer">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="size-6">
                                            <path fillRule="evenodd" d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                )}
                                <div className="h-10 flex items-center justify-center">
                                    <p className="font-bold text-white text-sm text-nowrap">{PlaybackTime} / {Duration}</p>
                                </div>
                            </div>
                            <div className="flex flex-row gap-3 items-center">
                                <Slider color="foreground" onChange={(value) => { document.querySelector("video").volume = value }} defaultValue={1} minValue={0} maxValue={1} step={0.1} />
                                <div className="w-10 h-10 flex items-center justify-center">
                                    <Tooltip content="Previews are not available in higher quality." showArrow={true} shadow="sm" delay={0} closeDelay={0}>
                                        <p className="font-bold text-white text-sm">360p</p>
                                    </Tooltip>
                                </div>
                                <div onClick={EnterFullscreen} className="w-10 h-10 flex items-center justify-center cursor-pointer mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" className="size-6" fill="white"><path d="M120-120v-200h80v120h120v80H120Zm520 0v-80h120v-120h80v200H640ZM120-640v-200h200v80H200v120h-80Zm640 0v-120H640v-80h200v200h-80Z" /></svg>
                                </div>
                            </div>
                        </div>
                    )}

                </div>
                <img className={"aspect-video w-auto rounded-medium cursor-pointer h-[360px] object-contain " + (PlayerStatus === "WAITING" ? "block" : "hidden")} ref={VideoImageRef} src={ImageURL}></img>
                <video ref={VideoRef} onTimeUpdate={HandlePlaybackTimeUpdate} onLoadedMetadata={SetPlaybackDuration} onEnded={() => { SetPlayerStatus("ENDED"); console.log("ENDED") }} onPlay={() => { SetPlayerStatus("PLAYING"); console.log("PLAYING") }} onPause={() => { SetPlayerStatus("PAUSED"); console.log("PAUSED") }} className={`rounded-medium h-[360px] ${PlayerStatus === "WAITING" ? "min-w-full hidden" : "pointer-events-none"}`}>
                    <source ref={VideoSrc} onError={() => { SetPlayerStatus("ERROR") }}></source>
                </video>
            </div >
        </div >
    )
}